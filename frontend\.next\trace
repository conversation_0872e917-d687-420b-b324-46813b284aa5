[{"name": "hot-reloader", "duration": 266, "timestamp": 83793056518, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752060872836, "traceId": "7daaea345a3bbf30"}, {"name": "setup-dev-bundler", "duration": 1482949, "timestamp": 83792572956, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752060872352, "traceId": "7daaea345a3bbf30"}, {"name": "run-instrumentation-hook", "duration": 52, "timestamp": 83794365343, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752060874145, "traceId": "7daaea345a3bbf30"}, {"name": "start-dev-server", "duration": 3572336, "timestamp": 83790848986, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "343166976", "memory.totalMem": "6307684352", "memory.heapSizeLimit": "3203399680", "memory.rss": "180961280", "memory.heapTotal": "102174720", "memory.heapUsed": "70011608"}, "startTime": 1752060870628, "traceId": "7daaea345a3bbf30"}, {"name": "compile-path", "duration": 20171600, "timestamp": 84986566490, "id": 7, "tags": {"trigger": "/auth"}, "startTime": 1752062066345, "traceId": "7daaea345a3bbf30"}, {"name": "ensure-page", "duration": 20192343, "timestamp": 84986552153, "id": 6, "parentId": 3, "tags": {"inputPage": "/auth/page"}, "startTime": 1752062066332, "traceId": "7daaea345a3bbf30"}]