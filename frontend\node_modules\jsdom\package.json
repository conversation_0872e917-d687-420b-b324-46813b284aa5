{"name": "jsdom", "version": "20.0.3", "description": "A JavaScript implementation of many web standards", "keywords": ["dom", "html", "whatwg", "w3c"], "maintainers": ["<PERSON> <<EMAIL>> (http://tmpvar.com)", "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "<PERSON> <<EMAIL>> (https://blog.smayr.name/)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://timothygu.me/)", "<PERSON><PERSON> <<EMAIL>> (https://zirro.se/)", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": "jsdom/jsdom", "dependencies": {"abab": "^2.0.6", "acorn": "^8.8.1", "acorn-globals": "^7.0.0", "cssom": "^0.5.0", "cssstyle": "^2.3.0", "data-urls": "^3.0.2", "decimal.js": "^10.4.2", "domexception": "^4.0.0", "escodegen": "^2.0.0", "form-data": "^4.0.0", "html-encoding-sniffer": "^3.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.2", "parse5": "^7.1.1", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^4.1.2", "w3c-xmlserializer": "^4.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^2.0.0", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0", "ws": "^8.11.0", "xml-name-validator": "^4.0.0"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "chai": "^4.3.7", "eslint": "^8.28.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-jsdom-internal": "link:./scripts/eslint-plugin", "js-yaml": "^4.1.0", "karma": "^6.4.1", "karma-browserify": "^8.1.0", "karma-chrome-launcher": "^3.1.1", "karma-mocha": "^2.0.1", "karma-mocha-webworker": "^1.3.0", "minimatch": "^5.1.0", "mocha": "^10.1.0", "mocha-sugar-free": "^1.4.0", "pngjs": "^6.0.0", "rimraf": "^3.0.2", "server-destroy": "^1.0.1", "watchify": "^4.0.0", "webidl2js": "^17.1.0", "yargs": "^17.6.2"}, "browser": {"canvas": false, "vm": "./lib/jsdom/vm-shim.js", "./lib/jsdom/living/websockets/WebSocket-impl.js": "./lib/jsdom/living/websockets/WebSocket-impl-browser.js"}, "scripts": {"prepare": "yarn convert-idl && yarn generate-js-globals", "pretest": "yarn prepare && yarn init-wpt", "test-wpt": "mocha test/web-platform-tests/run-wpts.js", "test-tuwpt": "mocha test/web-platform-tests/run-tuwpts.js", "test-mocha": "mocha", "test-api": "mocha test/api", "test": "mocha test/index.js", "test-browser-iframe": "karma start test/karma.conf.js", "test-browser-worker": "karma start test/karma-webworker.conf.js", "test-browser": "yarn test-browser-iframe && yarn test-browser-worker", "lint": "eslint . --cache --ext .js,.html", "init-wpt": "git submodule update --init --recursive", "reset-wpt": "rimraf ./test/web-platform-tests/tests && yarn init-wpt", "update-wpt": "git submodule update --recursive --remote && cd test/web-platform-tests/tests && python3 wpt.py manifest --path ../wpt-manifest.json", "update-authors": "git log --format=\"%aN <%aE>\" | sort -f | uniq > AUTHORS.txt", "benchmark": "node ./benchmark/runner", "benchmark-browser": "node ./benchmark/runner --bundle", "convert-idl": "node ./scripts/webidl/convert.js", "generate-js-globals": "node ./scripts/generate-js-globals.js"}, "main": "./lib/api.js", "engines": {"node": ">=14"}}