/**
 * @license React
 * react.shared-subset.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';

if (process.env.NODE_ENV !== "production") {
  (function() {
'use strict';

// eslint-disable-next-line react-internal/prod-error-codes
throw new Error('This entry point is not yet supported outside of experimental channels');
  })();
}
