{"name": "whatwg-mimetype", "description": "Parses, serializes, and manipulates MIME types, according to the WHATWG MIME Sniffing Standard", "keywords": ["content-type", "mime type", "<PERSON><PERSON><PERSON><PERSON>", "http", "whatwg"], "version": "3.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/whatwg-mimetype", "main": "lib/mime-type.js", "files": ["lib/"], "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "jest": "^27.2.0", "minipass-fetch": "^1.4.1", "printable-string": "^0.3.0", "whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"]}}