'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.TEST_TIMEOUT_SYMBOL =
  exports.STATE_SYM =
  exports.RETRY_TIMES =
  exports.LOG_ERRORS_BEFORE_RETRY =
    void 0;
var Symbol = globalThis['jest-symbol-do-not-touch'] || globalThis.Symbol;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

const STATE_SYM = Symbol('JEST_STATE_SYMBOL');
exports.STATE_SYM = STATE_SYM;
const RETRY_TIMES = Symbol.for('RETRY_TIMES');
// To pass this value from Runtime object to state we need to use global[sym]
exports.RETRY_TIMES = RETRY_TIMES;
const TEST_TIMEOUT_SYMBOL = Symbol.for('TEST_TIMEOUT_SYMBOL');
exports.TEST_TIMEOUT_SYMBOL = TEST_TIMEOUT_SYMBOL;
const LOG_ERRORS_BEFORE_RETRY = Symbol.for('LOG_ERRORS_BEFORE_RETRY');
exports.LOG_ERRORS_BEFORE_RETRY = LOG_ERRORS_BEFORE_RETRY;
