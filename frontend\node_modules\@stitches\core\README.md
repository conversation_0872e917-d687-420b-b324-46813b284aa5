# @stitches/core

**@stitches/core** is a framework-agnostic implementation of [stitches](https://stitches.dev), a CSS-in-JS library with a best-in-class developer experience.

<p><br /></p>

```sh
# with npm
npm install @stitches/core

# with yarn
yarn add @stitches/core
```

<p><br /></p>

<a href="https://codepen.io/embed/prefill?editable=true&default-tab=js%2Cresult&data=%7B%22js%22%3A%22import+%7B+css+%7D+from+%27https%3A%2F%2Fcdn.skypack.dev%2F%40stitches%2Fcore%27%3B%5Cn%5Cn%2F%2F+add+your+code+here%21%5Cn%2F%2F+see+https%3A%2F%2Fstitches.dev+for+documentation%22%7D"><img src="https://img.shields.io/badge/-open_in_codepen-2b354f?logo=codepen&style=flat-square" alt="open in codepen" valign="middle" align="right"></a>

<br />

```html
<script type="module">

import { css } from 'https://cdn.skypack.dev/@stitches/core'

</script>
```

<p><br /></p>

<a href="https://codepen.io/embed/prefill?editable=true&default-tab=js%2Cresult&data=%7B%22js%22%3A%22const+%7B+css+%7D+%3D+stitches%5Cn%5Cn%2F%2F+add+your+code+here%21%5Cn%2F%2F+see+https%3A%2F%2Fstitches.dev+for+documentation%22%2C%22js_external%22%3A%22https%3A%2F%2Funpkg.com%2F%40stitches%2Fcore%2Fdist%2Findex.global.js%22%7D"><img src="https://img.shields.io/badge/-open_in_codepen-2b354f?logo=codepen&style=flat-square" alt="open in codepen" valign="middle" align="right"></a>

<br />

```js
<script src="https://unpkg.com/@stitches/core/dist/index.global.js"></script>
<script>

const { css } = stitches

</script>
```

<p><br /></p>

## Documentation

For full documentation, visit [stitches.dev](https://stitches.dev).

## Contributing

Please follow our [contributing guidelines](./CONTRIBUTING.md).

## Authors

- Pedro Duarte ([@peduarte](https://twitter.com/peduarte)) - [Modulz](https://modulz.app)
- Abdulhadi Alhallak ([@hadi_hlk](https://twitter.com/hadi_hlk)) - [Modulz](https://modulz.app)
- Jonathan Neal ([@jon_neal](https://twitter.com/jon_neal)) - [Modulz](https://modulz.app)

## License

Licensed under the MIT License, Copyright © 2021-present Modulz.

See [LICENSE](./LICENSE.md) for more information.
