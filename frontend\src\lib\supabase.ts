import { createClient } from '@supabase/supabase-js'

// استخدام قيم افتراضية صحيحة للتطوير
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://xyzcompany.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5emNvbXBhbnkiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0NjkzNzIwMCwiZXhwIjoxOTYyNTEzMjAwfQ.demo-key'

// إنشاء عميل Supabase مع معالجة الأخطاء
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false // تعطيل الجلسة للتطوير
  }
})

// User roles enum
export enum UserRole {
  STUDENT = 'student',
  SCHOOL = 'school', 
  ADMIN = 'admin',
  DELIVERY = 'delivery'
}

// User profile type
export interface UserProfile {
  id: string
  email: string
  full_name: string
  role: UserRole
  phone?: string
  school_name?: string
  created_at: string
  updated_at: string
}

// Auth helper functions
export const authHelpers = {
  // Sign up with role
  async signUp(email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  },

  // Sign in
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current user
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  // Get user profile
  async getUserProfile(userId: string): Promise<{ profile: UserProfile | null, error: any }> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    return { profile: data, error }
  },

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<UserProfile>) {
    const { data, error } = await supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single()
    
    return { data, error }
  },

  // Check if user has role
  hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
    const roleHierarchy = {
      [UserRole.ADMIN]: 4,
      [UserRole.SCHOOL]: 3,
      [UserRole.DELIVERY]: 2,
      [UserRole.STUDENT]: 1
    }
    
    return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
  }
}
