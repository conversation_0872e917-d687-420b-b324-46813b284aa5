{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\n// استخدام قيم افتراضية صحيحة للتطوير\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://xyzcompany.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.demo-key'\n\n// إنشاء عميل Supabase مع معالجة الأخطاء\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    persistSession: false // تعطيل الجلسة للتطوير\n  }\n})\n\n// User roles enum\nexport enum UserRole {\n  STUDENT = 'student',\n  SCHOOL = 'school', \n  ADMIN = 'admin',\n  DELIVERY = 'delivery'\n}\n\n// User profile type\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name: string\n  role: UserRole\n  phone?: string\n  school_name?: string\n  created_at: string\n  updated_at: string\n}\n\n// Auth helper functions\nexport const authHelpers = {\n  // Sign up with role\n  async signUp(email: string, password: string, userData: {\n    full_name: string\n    role: UserRole\n    phone?: string\n    school_name?: string\n  }) {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: userData\n      }\n    })\n    return { data, error }\n  },\n\n  // Sign in\n  async signIn(email: string, password: string) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    })\n    return { data, error }\n  },\n\n  // Sign out\n  async signOut() {\n    const { error } = await supabase.auth.signOut()\n    return { error }\n  },\n\n  // Get current user\n  async getCurrentUser() {\n    const { data: { user }, error } = await supabase.auth.getUser()\n    return { user, error }\n  },\n\n  // Get user profile\n  async getUserProfile(userId: string): Promise<{ profile: UserProfile | null, error: any }> {\n    const { data, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n    \n    return { profile: data, error }\n  },\n\n  // Update user profile\n  async updateUserProfile(userId: string, updates: Partial<UserProfile>) {\n    const { data, error } = await supabase\n      .from('profiles')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', userId)\n      .select()\n      .single()\n    \n    return { data, error }\n  },\n\n  // Check if user has role\n  hasRole(userRole: UserRole, requiredRole: UserRole): boolean {\n    const roleHierarchy = {\n      [UserRole.ADMIN]: 4,\n      [UserRole.SCHOOL]: 3,\n      [UserRole.DELIVERY]: 2,\n      [UserRole.STUDENT]: 1\n    }\n    \n    return roleHierarchy[userRole] >= roleHierarchy[requiredRole]\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,qCAAqC;AACrC,MAAM,cAAc,iEAAwC;AAC5D,MAAM,kBAAkB,8DAA6C;AAG9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,MAAM;QACJ,gBAAgB,MAAM,uBAAuB;IAC/C;AACF;AAGO,IAAA,AAAK,kCAAA;;;;;WAAA;;AAoBL,MAAM,cAAc;IACzB,oBAAoB;IACpB,MAAM,QAAO,KAAa,EAAE,QAAgB,EAAE,QAK7C;QACC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,UAAU;IACV,MAAM,QAAO,KAAa,EAAE,QAAgB;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,WAAW;IACX,MAAM;QACJ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mBAAmB;IACnB,MAAM,gBAAe,MAAc;QACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,MAAc,EAAE,OAA6B;QACnE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,yBAAyB;IACzB,SAAQ,QAAkB,EAAE,YAAsB;QAChD,MAAM,gBAAgB;YACpB,SAAgB,EAAE;YAClB,UAAiB,EAAE;YACnB,YAAmB,EAAE;YACrB,WAAkB,EAAE;QACtB;QAEA,OAAO,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,aAAa;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { UserProfile, UserRole } from '@/lib/supabase'\n\n// نوع مبسط للمستخدم للتطوير\ninterface User {\n  id: string\n  email?: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signUp: (email: string, password: string, userData: {\n    full_name: string\n    role: UserRole\n    phone?: string\n    school_name?: string\n  }) => Promise<{ data: unknown, error: string | null }>\n  signIn: (email: string, password: string) => Promise<{ data: unknown, error: string | null }>\n  signOut: () => Promise<{ error: string | null }>\n  updateProfile: (updates: Partial<UserProfile>) => Promise<{ data: unknown, error: string | null }>\n  hasRole: (requiredRole: UserRole) => boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(false) // تعطيل التحميل للتطوير\n\n  useEffect(() => {\n    // للتطوير - لا نحتاج Supabase الآن\n    setLoading(false)\n  }, [])\n\n  const signUp = async (email: string, password: string, userData: {\n    full_name: string\n    role: UserRole\n    phone?: string\n    school_name?: string\n  }) => {\n    // للتطوير - محاكاة تسجيل حساب جديد\n    console.log('Sign up:', email, userData)\n    return { data: { user: { id: '1', email } }, error: null }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    // للتطوير - محاكاة تسجيل الدخول\n    console.log('Sign in:', email)\n    const mockUser = { id: '1', email }\n    const mockProfile: UserProfile = {\n      id: '1',\n      email,\n      full_name: 'مستخدم تجريبي',\n      role: UserRole.STUDENT,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n    setUser(mockUser)\n    setProfile(mockProfile)\n    return { data: { user: mockUser }, error: null }\n  }\n\n  const signOut = async () => {\n    setUser(null)\n    setProfile(null)\n    return { error: null }\n  }\n\n  const updateProfile = async (updates: Partial<UserProfile>) => {\n    if (!user) return { data: null, error: 'No user logged in' }\n\n    const updatedProfile = { ...profile, ...updates } as UserProfile\n    setProfile(updatedProfile)\n    return { data: updatedProfile, error: null }\n  }\n\n  const hasRole = (requiredRole: UserRole): boolean => {\n    if (!profile) return false\n\n    // نظام هرمي بسيط للأدوار\n    const roleHierarchy = {\n      [UserRole.ADMIN]: 4,\n      [UserRole.SCHOOL]: 3,\n      [UserRole.DELIVERY]: 2,\n      [UserRole.STUDENT]: 1\n    }\n\n    return roleHierarchy[profile.role] >= roleHierarchy[requiredRole]\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    hasRole\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA2BA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,wBAAwB;;IAEtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mCAAmC;QACnC,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe,UAAkB;QAMrD,mCAAmC;QACnC,QAAQ,GAAG,CAAC,YAAY,OAAO;QAC/B,OAAO;YAAE,MAAM;gBAAE,MAAM;oBAAE,IAAI;oBAAK;gBAAM;YAAE;YAAG,OAAO;QAAK;IAC3D;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,YAAY;QACxB,MAAM,WAAW;YAAE,IAAI;YAAK;QAAM;QAClC,MAAM,cAA2B;YAC/B,IAAI;YACJ;YACA,WAAW;YACX,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO;YACtB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,QAAQ;QACR,WAAW;QACX,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,MAAM,UAAU;QACd,QAAQ;QACR,WAAW;QACX,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,MAAM;YAAM,OAAO;QAAoB;QAE3D,MAAM,iBAAiB;YAAE,GAAG,OAAO;YAAE,GAAG,OAAO;QAAC;QAChD,WAAW;QACX,OAAO;YAAE,MAAM;YAAgB,OAAO;QAAK;IAC7C;IAEA,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,SAAS,OAAO;QAErB,yBAAyB;QACzB,MAAM,gBAAgB;YACpB,CAAC,sHAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;YAClB,CAAC,sHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;YACnB,CAAC,sHAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,EAAE;YACrB,CAAC,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,EAAE;QACtB;QAEA,OAAO,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI,aAAa,CAAC,aAAa;IACnE;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}