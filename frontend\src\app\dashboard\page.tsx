"use client"

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import { UserRole } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { UserMenu } from '@/components/auth/UserMenu'
import { 
  GraduationCap, 
  Users, 
  Package, 
  Truck, 
  BarChart3, 
  Settings,
  School,
  Shield
} from 'lucide-react'

export default function DashboardPage() {
  const { profile } = useAuth()
  const { t } = useTranslation()

  const getDashboardContent = () => {
    if (!profile) return null

    switch (profile.role) {
      case UserRole.ADMIN:
        return <AdminDashboard />
      case UserRole.SCHOOL:
        return <SchoolDashboard />
      case UserRole.DELIVERY:
        return <DeliveryDashboard />
      case UserRole.STUDENT:
        return <StudentDashboard />
      default:
        return <div>لوحة تحكم غير معروفة</div>
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t('navigation.dashboard')}
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <LanguageToggle />
              <ThemeToggle />
              <UserMenu />
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              مرحباً، {profile?.full_name}
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              إليك نظرة عامة على حسابك وأنشطتك
            </p>
          </div>

          {getDashboardContent()}
        </main>
      </div>
    </ProtectedRoute>
  )
}

function AdminDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,234</div>
          <p className="text-xs text-muted-foreground">+12% من الشهر الماضي</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">الطلبات النشطة</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">89</div>
          <p className="text-xs text-muted-foreground">+5% من الأسبوع الماضي</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">المدارس المسجلة</CardTitle>
          <School className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">45</div>
          <p className="text-xs text-muted-foreground">+3 مدارس جديدة</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">الإيرادات</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">45,231 درهم</div>
          <p className="text-xs text-muted-foreground">+18% من الشهر الماضي</p>
        </CardContent>
      </Card>
    </div>
  )
}

function SchoolDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <Card>
        <CardHeader>
          <CardTitle>طلاب المدرسة</CardTitle>
          <CardDescription>إجمالي الطلاب المسجلين</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">156</div>
          <p className="text-sm text-muted-foreground">طالب مسجل</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>الطلبات المعلقة</CardTitle>
          <CardDescription>طلبات تحتاج موافقة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">12</div>
          <p className="text-sm text-muted-foreground">طلب في الانتظار</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>الطلبات المكتملة</CardTitle>
          <CardDescription>طلبات تم تسليمها</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">89</div>
          <p className="text-sm text-muted-foreground">طلب مكتمل</p>
        </CardContent>
      </Card>
    </div>
  )
}

function DeliveryDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <Card>
        <CardHeader>
          <CardTitle>طلبات التوصيل</CardTitle>
          <CardDescription>طلبات جاهزة للتوصيل</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">23</div>
          <p className="text-sm text-muted-foreground">طلب جاهز</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>قيد التوصيل</CardTitle>
          <CardDescription>طلبات في الطريق</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">8</div>
          <p className="text-sm text-muted-foreground">طلب في الطريق</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>تم التسليم</CardTitle>
          <CardDescription>طلبات مسلمة اليوم</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">15</div>
          <p className="text-sm text-muted-foreground">طلب مسلم</p>
        </CardContent>
      </Card>
    </div>
  )
}

function StudentDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <Card>
        <CardHeader>
          <CardTitle>طلباتي</CardTitle>
          <CardDescription>طلبات أزياء التخرج الخاصة بي</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">2</div>
          <p className="text-sm text-muted-foreground">طلب نشط</p>
          <Button className="mt-4 w-full">عرض الطلبات</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>التخصيص</CardTitle>
          <CardDescription>خصص زي التخرج الخاص بك</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            اختر الألوان والأنماط المفضلة لديك
          </p>
          <Button className="w-full">ابدأ التخصيص</Button>
        </CardContent>
      </Card>
    </div>
  )
}
